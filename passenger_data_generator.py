#!/usr/bin/env python3
"""
Passenger Data Generator
生成乘客OD需求数据的Excel文件
"""

import pandas as pd
import numpy as np
import random
from typing import Dict, List, Tuple
import os

def generate_passenger_demand_data(routes_data: Dict[int, List], 
                                 time_horizon: int = 180,
                                 peak_periods: List[Tuple[int, int]] = None) -> pd.DataFrame:
    """
    生成乘客OD需求数据
    
    Args:
        routes_data: 路线数据字典 {route_id: [stops]}
        time_horizon: 时间范围（分钟）
        peak_periods: 高峰时段列表 [(start_min, end_min), ...]
    
    Returns:
        包含OD需求数据的DataFrame
    """
    
    if peak_periods is None:
        peak_periods = [(0, 60)]  # 默认高峰时段
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    
    od_records = []
    
    for route_id, stops in routes_data.items():
        print(f"正在生成Route {route_id}的乘客需求数据...")
        
        # 为每分钟生成OD需求
        for minute in range(time_horizon):
            
            # 判断是否为高峰时段
            is_peak = any(start <= minute <= end for start, end in peak_periods)
            
            # 为每个起点站生成需求
            for i, origin_stop in enumerate(stops[:-1]):  # 排除最后一站作为起点
                
                # 为每个可能的终点站生成需求
                for j, dest_stop in enumerate(stops[i+1:], i+1):
                    
                    # 计算基础需求率（基于距离）
                    distance_factor = j - i  # 站点距离
                    base_demand_rate = max(0.1, 1 / distance_factor)  # 距离越远，需求越少
                    
                    # 高峰时段需求增加
                    if is_peak:
                        demand_rate = base_demand_rate * 0.8
                    else:
                        # 最后一小时需求减少
                        if minute >= time_horizon - 60:
                            demand_rate = base_demand_rate * 0.2
                        else:
                            demand_rate = base_demand_rate * 0.5
                    
                    # 生成泊松分布的乘客数量
                    passenger_count = np.random.poisson(demand_rate)
                    
                    # 只记录有乘客的需求
                    if passenger_count > 0:
                        od_records.append({
                            'route_id': route_id,
                            'origin_stop': origin_stop,
                            'destination_stop': dest_stop,
                            'minute': minute,
                            'passenger_count': passenger_count,
                            'is_peak_period': is_peak,
                            'distance_stops': distance_factor
                        })
    
    df = pd.DataFrame(od_records)
    print(f"✅ 生成了 {len(df)} 条OD需求记录")
    
    return df

def load_route_stops_from_excel(filename: str = "route_information_random.xlsx", mode: str = "1") -> Dict[int, List]:
    """
    从路线Excel文件中读取站点信息
    
    Args:
        filename: 路线信息Excel文件
    
    Returns:
        路线站点字典 {route_id: [stop_ids]}
    """
    
    if mode == "2":
        df = pd.read_excel(filename, sheet_name='All_Routes')
    else: 
        df = pd.read_excel(filename, sheet_name='All_Routes')

    try:
        routes_data = {}
        
        for route_id in df['route_id'].unique():
            route_data = df[df['route_id'] == route_id].sort_values('position_on_route')
            stop_ids = route_data['stop_id'].tolist()
            routes_data[int(route_id)] = stop_ids
            
        print(f"✅ 从 {filename} 读取了 {len(routes_data)} 条路线的站点数据")
        return routes_data
        
    except Exception as e:
        print(f"❌ 读取路线文件失败: {str(e)}")
        return {}

def save_passenger_demand_to_excel(df: pd.DataFrame, filename: str = "passenger_demand_random.xlsx"):
    """
    将乘客需求数据保存为Excel文件
    
    Args:
        df: 乘客需求数据DataFrame
        filename: 输出文件名
    """
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 主要数据表
        df.to_excel(writer, sheet_name='OD_Matrix', index=False)
        
        # 创建汇总统计表
        summary_data = create_demand_summary(df)
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        # 创建按路线汇总的表
        route_summary = df.groupby('route_id').agg({
            'passenger_count': ['sum', 'mean', 'count'],
            'minute': ['min', 'max']
        }).round(2)
        route_summary.columns = ['Total_Passengers', 'Avg_Per_Record', 'Record_Count', 'First_Minute', 'Last_Minute']
        route_summary.to_excel(writer, sheet_name='Route_Summary')
        
        # 创建时间分布表
        time_summary = df.groupby('minute')['passenger_count'].sum().reset_index()
        time_summary.to_excel(writer, sheet_name='Time_Distribution', index=False)
        
        # 设置列宽
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    if cell.value:
                        max_length = max(max_length, len(str(cell.value)))
                adjusted_width = min(max_length + 2, 20)
                worksheet.column_dimensions[column_letter].width = adjusted_width

def create_demand_summary(df: pd.DataFrame) -> Dict:
    """创建需求汇总统计"""
    
    total_passengers = df['passenger_count'].sum()
    total_records = len(df)
    unique_routes = df['route_id'].nunique()
    unique_od_pairs = df[['route_id', 'origin_stop', 'destination_stop']].drop_duplicates().shape[0]
    peak_passengers = df[df['is_peak_period'] == True]['passenger_count'].sum()
    off_peak_passengers = df[df['is_peak_period'] == False]['passenger_count'].sum()
    
    avg_passengers_per_minute = df.groupby('minute')['passenger_count'].sum().mean()
    max_passengers_per_minute = df.groupby('minute')['passenger_count'].sum().max()
    
    summary = {
        '统计项目': [
            '总乘客数量',
            '总需求记录数',
            '涉及路线数',
            '唯一OD对数',
            '高峰时段乘客数',
            '非高峰时段乘客数',
            '高峰时段占比 (%)',
            '平均每分钟乘客数',
            '最大每分钟乘客数',
            '平均每条记录乘客数'
        ],
        '数值': [
            int(total_passengers),
            int(total_records),
            int(unique_routes),
            int(unique_od_pairs),
            int(peak_passengers),
            int(off_peak_passengers),
            round(peak_passengers / total_passengers * 100, 1) if total_passengers > 0 else 0,
            round(avg_passengers_per_minute, 2),
            int(max_passengers_per_minute),
            round(total_passengers / total_records, 2) if total_records > 0 else 0
        ]
    }
    
    return summary


def main():
    """主函数"""
    
    print("🚌 乘客需求数据生成器")
    print("=" * 50)
    
    # 选择数据源
    mode = input("请选择数据源 (1: 使用random数据, 2: 使用example数据): ").strip()
    
    if mode == "1":
        # 从路线文件生成
        route_file = "route_information_random.xlsx"        
        routes_data = load_route_stops_from_excel(route_file, mode)
        
        if routes_data:
            time_horizon = int(input("请输入时间范围(分钟) (默认180): ") or "180")
            
            print(f"\n正在基于 {route_file} 生成乘客需求数据...")
            df = generate_passenger_demand_data(routes_data, time_horizon)
            filename = "passenger_demand_random.xlsx"
        else:
            print("无法读取路线数据，退出...")
            return
            
    else:
        # 从路线文件生成
        route_file = "route_information_example.xlsx"        
        routes_data = load_route_stops_from_excel(route_file, mode)

        if routes_data:
            time_horizon = int(input("请输入时间范围(分钟) (默认180): ") or "180")
            
            print(f"\n正在基于 {route_file} 生成乘客需求数据...")
            df = generate_passenger_demand_data(routes_data, time_horizon)
            filename = "passenger_demand_example.xlsx"
        else:
            print("无法读取路线数据，退出...")
            return
    
    # 导出乘客需求汇总数据
    summary_data = create_demand_summary(df)
    summary_df = pd.DataFrame(summary_data)
    print(f"\n📊 乘客需求汇总数据:")
    print(summary_df.to_string(index=False))
    
    # 保存到Excel文件
    print(f"\n正在保存到 {filename}...")
    save_passenger_demand_to_excel(df, filename)
    
    print("✅ Excel文件生成完成!")
    print(f"📁 文件位置: {os.path.abspath(filename)}")
    

if __name__ == "__main__":
    main()
