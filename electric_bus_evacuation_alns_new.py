#!/usr/bin/env python3
"""
High-Performance Electric Bus Evacuation Scheduling - ALNS Algorithm
高性能电动公交疏散调度 - ALNS算法

优化策略：
1. 矩阵化计算 - 使用NumPy向量化操作
2. 并行处理 - 多进程/多线程
3. GPU加速 - CuPy支持（可选）
4. 缓存优化 - 预计算和结果缓存
5. 内存优化 - 减少对象创建和复制
"""

import numpy as np
import pandas as pd
import random
import copy
import time
import os
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Optional, Union
from enum import Enum
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import threading
from functools import lru_cache
import warnings

# GPU加速支持（可选）
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("🚀 GPU加速可用 (CuPy)")
except ImportError:
    cp = np
    GPU_AVAILABLE = False
    print("💻 使用CPU计算")

# 并行处理配置
NUM_CORES = mp.cpu_count()
PARALLEL_OPERATORS = min(4, NUM_CORES)  # 并行算子数量
BATCH_SIZE = 32  # 批处理大小

# 从原文件导入基础类
from electric_bus_evacuation_alns import (
    ChargerType, Depot, Stop, Route, Bus, Passenger, Trip, 
    ChargingEvent, BusTour, Solution, PassengerQueue,
    load_depot_data_from_excel, load_route_data_from_excel,
    create_passengers_from_excel_data, load_passenger_data_from_excel,
    create_passengers_random_fallback
)

class HighPerformanceOperatorEvaluator:
    """高性能算子评估器 - 使用矩阵化计算"""
    
    def __init__(self):
        # 使用NumPy数组存储性能数据
        self.destroy_operators = ['random_trip_removal', 'route_based_removal', 'time_based_removal']
        self.repair_operators = ['greedy_insertion', 'regret_insertion', 'random_insertion']
        
        # 矩阵化存储：[uses, improvements, total_improvement]
        self.destroy_stats = np.zeros((len(self.destroy_operators), 3))
        self.repair_stats = np.zeros((len(self.repair_operators), 4))  # 额外的repair_efficiency
        
        # 算子索引映射
        self.destroy_idx = {op: i for i, op in enumerate(self.destroy_operators)}
        self.repair_idx = {op: i for i, op in enumerate(self.repair_operators)}
        
        # 权重向量
        self.destroy_weights = np.ones(len(self.destroy_operators))
        self.repair_weights = np.ones(len(self.repair_operators))
        
        # 性能参数
        self.learning_rate = 0.1
        self.weight_update_frequency = 50
        self.iteration_count = 0
        self.min_weight = 0.1
    
    def record_destroy_performance(self, operator: str, improvement: float):
        """记录破坏算子性能"""
        idx = self.destroy_idx[operator]
        self.destroy_stats[idx, 0] += 1  # uses
        if improvement > 0:
            self.destroy_stats[idx, 1] += 1  # improvements
        self.destroy_stats[idx, 2] += improvement  # total_improvement
    
    def record_repair_performance(self, operator: str, improvement: float, repair_efficiency: float = 0.0):
        """记录修复算子性能"""
        idx = self.repair_idx[operator]
        self.repair_stats[idx, 0] += 1  # uses
        if improvement > 0:
            self.repair_stats[idx, 1] += 1  # improvements
        self.repair_stats[idx, 2] += improvement  # total_improvement
        self.repair_stats[idx, 3] += repair_efficiency  # repair_efficiency
    
    def update_weights_vectorized(self) -> Tuple[np.ndarray, np.ndarray]:
        """向量化权重更新"""
        self.iteration_count += 1
        
        if self.iteration_count % self.weight_update_frequency != 0:
            return self.destroy_weights, self.repair_weights
        
        # 向量化计算性能分数
        destroy_uses = self.destroy_stats[:, 0]
        destroy_improvements = self.destroy_stats[:, 1]
        destroy_total_imp = self.destroy_stats[:, 2]
        
        # 避免除零
        destroy_uses = np.maximum(destroy_uses, 1)
        destroy_scores = (destroy_improvements / destroy_uses) * 0.5 + \
                        (destroy_total_imp / destroy_uses) * 0.5
        
        # 更新权重（向量化）
        self.destroy_weights = self.destroy_weights + \
                              self.learning_rate * (destroy_scores - self.destroy_weights)
        self.destroy_weights = np.maximum(self.destroy_weights, self.min_weight)
        
        # 修复算子权重更新
        repair_uses = self.repair_stats[:, 0]
        repair_improvements = self.repair_stats[:, 1]
        repair_total_imp = self.repair_stats[:, 2]
        
        repair_uses = np.maximum(repair_uses, 1)
        repair_scores = (repair_improvements / repair_uses) * 0.5 + \
                       (repair_total_imp / repair_uses) * 0.5
        
        self.repair_weights = self.repair_weights + \
                             self.learning_rate * (repair_scores - self.repair_weights)
        self.repair_weights = np.maximum(self.repair_weights, self.min_weight)
        
        return self.destroy_weights, self.repair_weights

class MatrixizedTripEvaluator:
    """矩阵化行程评估器"""
    
    def __init__(self, routes: Dict[int, Route], buses: List[Bus]):
        self.routes = routes
        self.buses = buses
        
        # 预计算路线矩阵
        self._precompute_route_matrices()
        
        # 缓存
        self._trip_cache = {}
        self._feasibility_cache = {}
    
    def _precompute_route_matrices(self):
        """预计算路线相关矩阵"""
        max_route_id = max(self.routes.keys()) + 1
        max_stops = max(len(route.stops) for route in self.routes.values())
        
        # 路线-站点矩阵
        self.route_stop_matrix = np.zeros((max_route_id, max_stops), dtype=int)
        self.travel_time_matrix = np.zeros((max_route_id, max_stops))
        self.energy_matrix = np.zeros((max_route_id, max_stops))
        
        for route_id, route in self.routes.items():
            for i, stop in enumerate(route.stops):
                self.route_stop_matrix[route_id, i] = stop.id
            
            for i, travel_time in enumerate(route.travel_times):
                self.travel_time_matrix[route_id, i] = travel_time
            
            for i, energy in enumerate(route.energy_consumption):
                self.energy_matrix[route_id, i] = energy
    
    @lru_cache(maxsize=1024)
    def evaluate_trip_batch(self, route_ids: tuple, start_times: tuple, 
                           bus_ids: tuple) -> np.ndarray:
        """批量评估行程可行性"""
        n_trips = len(route_ids)
        feasibility = np.zeros(n_trips, dtype=bool)
        
        for i in range(n_trips):
            # 简化的可行性检查
            route_id = route_ids[i]
            bus_id = bus_ids[i]
            
            if route_id in self.routes and bus_id < len(self.buses):
                route = self.routes[route_id]
                bus = self.buses[bus_id]
                
                # 估算能耗
                total_energy = np.sum(self.energy_matrix[route_id, :len(route.energy_consumption)])
                
                # 简化可行性检查
                feasibility[i] = total_energy < (bus.max_soc - bus.min_soc) * 0.8
        
        return feasibility

class ParallelDestroyOperators:
    """并行破坏算子"""
    
    @staticmethod
    def parallel_random_removal(solutions: List[Solution], num_remove_list: List[int]) -> List[Solution]:
        """并行随机移除"""
        def remove_trips(args):
            solution, num_remove = args
            new_solution = solution.copy()
            
            # 收集所有行程
            all_trips = []
            for bus_tour in new_solution.bus_tours:
                for i, trip in enumerate(bus_tour.trips):
                    all_trips.append((bus_tour.bus_id, i))
            
            if not all_trips:
                return new_solution
            
            # 随机选择要移除的行程
            num_to_remove = min(num_remove, len(all_trips))
            trips_to_remove = random.sample(all_trips, num_to_remove)
            
            # 按索引倒序排序，避免索引问题
            trips_to_remove.sort(key=lambda x: x[1], reverse=True)
            
            # 移除行程
            for bus_id, trip_idx in trips_to_remove:
                if trip_idx < len(new_solution.bus_tours[bus_id].trips):
                    new_solution.bus_tours[bus_id].trips.pop(trip_idx)
            
            return new_solution
        
        # 并行处理
        with ThreadPoolExecutor(max_workers=PARALLEL_OPERATORS) as executor:
            args_list = list(zip(solutions, num_remove_list))
            results = list(executor.map(remove_trips, args_list))
        
        return results
    
    @staticmethod
    def parallel_route_based_removal(solutions: List[Solution], num_remove_list: List[int], 
                                   routes: Dict[int, Route]) -> List[Solution]:
        """并行基于路线的移除"""
        def remove_by_route(args):
            solution, num_remove = args
            new_solution = solution.copy()
            
            if not routes:
                return new_solution
            
            # 随机选择路线
            target_route = random.choice(list(routes.keys()))
            
            # 移除该路线的行程
            removed_count = 0
            for bus_tour in new_solution.bus_tours:
                trips_to_remove = []
                for i, trip in enumerate(bus_tour.trips):
                    if trip.route_id == target_route and removed_count < num_remove:
                        trips_to_remove.append(i)
                        removed_count += 1
                
                # 倒序移除
                for i in reversed(trips_to_remove):
                    bus_tour.trips.pop(i)
            
            return new_solution
        
        with ThreadPoolExecutor(max_workers=PARALLEL_OPERATORS) as executor:
            args_list = list(zip(solutions, num_remove_list))
            results = list(executor.map(remove_by_route, args_list))
        
        return results

class VectorizedRepairOperators:
    """向量化修复算子"""
    
    def __init__(self, routes: Dict[int, Route], buses: List[Bus], 
                 planning_horizon: float):
        self.routes = routes
        self.buses = buses
        self.planning_horizon = planning_horizon
        self.trip_evaluator = MatrixizedTripEvaluator(routes, buses)
    
    def vectorized_greedy_insertion(self, solutions: List[Solution]) -> List[Solution]:
        """向量化贪心插入"""
        results = []
        
        # 批量处理解
        for solution in solutions:
            new_solution = self._greedy_insert_single(solution)
            results.append(new_solution)
        
        return results
    
    def _greedy_insert_single(self, solution: Solution) -> Solution:
        """单个解的贪心插入（修复版）"""
        new_solution = solution.copy()

        # 注意：这个方法应该使用实际的乘客队列，但为了简化，我们暂时禁用它
        # 在实际应用中，应该使用类似原版本的逻辑来处理乘客上下车

        # 暂时返回原解，避免产生虚假的乘客数据
        return new_solution

class HighPerformanceElectricBusEvacuationALNS:
    """高性能电动公交疏散调度ALNS算法"""
    
    def __init__(self, buses: List[Bus], routes: List[Route], depots: List[Depot],
                 passengers: List[Passenger], planning_horizon: float, return_buffer: float):
        
        # 基础参数
        self.buses = buses
        self.routes = {route.id: route for route in routes}
        self.depots = depots
        self.passengers = passengers
        self.planning_horizon = planning_horizon
        self.return_buffer = return_buffer
        
        # 性能优化组件
        self.operator_evaluator = HighPerformanceOperatorEvaluator()
        self.destroy_operators = ParallelDestroyOperators()
        self.repair_operators = VectorizedRepairOperators(self.routes, buses, planning_horizon)

        # Operator weights (will be adaptively updated) - from original version
        self.destroy_weights = {
            'random_trip_removal': 1.0,
            'route_based_removal': 1.0,
            'time_based_removal': 1.0
        }
        self.repair_weights = {
            'greedy_insertion': 1.0,
            'regret_insertion': 1.0
        }
        
        # ALNS参数
        self.max_iterations = 100
        self.temperature_start = 100.0
        self.temperature_end = 1.0
        self.alpha = 0.9995
        
        # 并行参数
        self.batch_size = BATCH_SIZE
        self.parallel_solutions = min(8, NUM_CORES)  # 并行解数量

        # 添加原版本中的其他参数
        self.boarding_time_per_person = 0.05  # Boarding/alighting time for each passenger (minutes)
        self.base_time = 0.1  # Base dwell time at each stop (minutes)
        self.soc_min_proportion = 0.2  # Minimum state of charge proportion for bus operation

        # Penalty weights
        self.vehicle_usage_penalty_weight = 50  # Penalty per bus used (encourage fewer buses)
        
        # 缓存
        self._solution_cache = {}
        self._evaluation_cache = {}

        # Initialize passenger queues based on arrival times (from original version)
        # Sort passengers by origin_stop first, then by arrival_time to ensure FIFO within each stop
        passengers_sorted = sorted(passengers, key=lambda p: (p.origin_stop, p.arrival_time))

        self.initial_passenger_queues = PassengerQueue()
        for passenger in passengers_sorted:
            self.initial_passenger_queues.add_passenger(passenger.origin_stop, passenger)

        print(f"🚀 高性能ALNS初始化完成")
        print(f"   CPU核心数: {NUM_CORES}")
        print(f"   并行算子数: {PARALLEL_OPERATORS}")
        print(f"   批处理大小: {self.batch_size}")
        print(f"   GPU加速: {'启用' if GPU_AVAILABLE else '禁用'}")
    
    def generate_initial_solution_fast(self) -> Solution:
        """快速生成初始解 - 修复乘客处理逻辑"""
        solution = Solution(self.buses)
        solution.passenger_queues = copy.deepcopy(self.initial_passenger_queues)

        # 使用原版本的贪心构造逻辑，但简化版本
        current_time = 0.0
        trip_interval = 10.0  # minutes between trips

        for bus in self.buses:
            bus_tour = solution.bus_tours[bus.id]
            current_bus_time = current_time
            current_soc = bus.max_soc

            # Keep adding trips while feasible
            while current_bus_time + trip_interval <= self.planning_horizon:
                # Try to create a trip on each route
                best_trip = None
                best_passengers = 0

                for route_id in self.routes:
                    # 使用原版本的trip创建逻辑
                    trip_candidate = self._create_trip_candidate_simplified(
                        route_id, current_bus_time, bus, current_soc, solution.passenger_queues
                    )

                    if trip_candidate and self._is_trip_feasible_simplified(trip_candidate, bus, current_soc):
                        # Count potential passengers (正确的计算方式)
                        potential_passengers = sum(len(p) for p in trip_candidate.passengers_boarded)
                        if potential_passengers > best_passengers:
                            best_trip = trip_candidate
                            best_passengers = potential_passengers

                if best_trip:
                    bus_tour.trips.append(best_trip)
                    current_bus_time = best_trip.departure_times[-1] + self._get_deadhead_time_to_depot_simplified(
                        best_trip.route_id, bus.home_depot
                    )
                    current_soc -= best_trip.energy_consumed

                    # Add charging if needed
                    if current_soc < bus.max_soc * self.soc_min_proportion:  # Charge when below 20%
                        charging_event = self._plan_charging_simplified(bus, current_soc, current_bus_time)
                        if charging_event:
                            bus_tour.charging_events.append(charging_event)
                            current_soc += charging_event.energy_added
                            current_bus_time = charging_event.end_time
                else:
                    break

            # Set final return time
            bus_tour.final_return_time = current_bus_time

        # 评估解
        self._evaluate_solution_fast(solution)
        return solution

    def _create_trip_candidate_simplified(self, route_id: int, departure_time: float,
                                        bus: Bus, current_soc: float, passenger_queues: PassengerQueue) -> Optional[Trip]:
        """简化版本的行程候选创建"""
        route = self.routes[route_id]

        trip = Trip(
            route_id=route_id,
            departure_time=departure_time,
            arrival_times=[],
            departure_times=[],
            passenger_load=[],
            passengers_boarded=[[] for _ in route.stops],
            passengers_alighted=[[] for _ in route.stops]
        )

        current_time = departure_time
        current_load = 0
        total_energy = 0.0

        for i, stop in enumerate(route.stops):
            # Travel to stop (except first stop)
            if i > 0:
                travel_time = route.travel_times[i-1]
                energy_consumption = route.energy_consumption[i-1]
                current_time += travel_time
                total_energy += energy_consumption

            trip.arrival_times.append(current_time)

            # Passenger alighting (简化版本)
            alighting_passengers = []
            for p in self._get_passengers_on_bus_simplified(trip, i):
                if p.destination_stop == stop.id:
                    alighting_passengers.append(p)
            trip.passengers_alighted[i] = alighting_passengers
            current_load -= len(alighting_passengers)

            # Passenger boarding
            boarded_passengers = passenger_queues.board_passengers(
                stop.id, bus.capacity, current_load
            )
            trip.passengers_boarded[i] = boarded_passengers
            current_load += len(boarded_passengers)

            # Calculate dwell time
            dwell_time = self._calculate_dwell_time_simplified(
                len(boarded_passengers), len(alighting_passengers)
            )
            current_time += dwell_time

            trip.departure_times.append(current_time)
            trip.passenger_load.append(current_load)

        trip.energy_consumed = total_energy
        return trip

    def _get_passengers_on_bus_simplified(self, trip: Trip, stop_index: int) -> List[Passenger]:
        """简化版本的车上乘客获取"""
        passengers_on_bus = []

        # Add passengers boarded at previous stops who haven't alighted yet
        for i in range(stop_index):
            for passenger in trip.passengers_boarded[i]:
                # Check if passenger hasn't alighted yet
                alighted = False
                for j in range(i+1, stop_index+1):
                    if passenger in trip.passengers_alighted[j]:
                        alighted = True
                        break
                if not alighted:
                    passengers_on_bus.append(passenger)

        return passengers_on_bus

    def _calculate_dwell_time_simplified(self, boarding_count: int, alighting_count: int) -> float:
        """简化版本的停站时间计算"""
        return self.base_time + max(boarding_count, alighting_count) * self.boarding_time_per_person

    def _get_deadhead_time_to_depot_simplified(self, route_id: int, depot_id: int) -> float:
        """简化版本的空驶时间"""
        return 10.0

    def _is_trip_feasible_simplified(self, trip: Trip, bus: Bus, current_soc: float) -> bool:
        """简化版本的行程可行性检查"""
        # Check SoC constraint
        if current_soc - trip.energy_consumed < bus.min_soc:
            return False

        # Check capacity constraint
        max_load = max(trip.passenger_load) if trip.passenger_load else 0
        if max_load > bus.capacity:
            return False

        return True

    def _plan_charging_simplified(self, bus: Bus, current_soc: float, start_time: float) -> Optional[ChargingEvent]:
        """简化版本的充电规划"""
        depot = next(d for d in self.depots if d.id == bus.home_depot)

        # Calculate adaptive charging ratio based on remaining time
        remaining_time = self.planning_horizon - start_time
        time_ratio = remaining_time / self.planning_horizon

        # Charging ratio: more remaining time = higher ratio (0.2-1.0)
        charging_ratio = 0.2 + 0.8 * time_ratio

        # Calculate target SoC and energy needed
        target_soc = current_soc + (bus.max_soc - current_soc) * charging_ratio
        energy_needed = target_soc - current_soc

        # Try different charger types (prefer faster charging)
        for charger_type in [ChargerType.TYPE_C, ChargerType.TYPE_B, ChargerType.TYPE_A]:
            if charger_type in depot.chargers and depot.chargers[charger_type] > 0:
                charging_power = depot.charging_power[charger_type]
                charging_time = energy_needed / charging_power * 60  # Convert to minutes

                end_time = start_time + charging_time

                # Check if charger is available (simplified check)
                return ChargingEvent(
                    depot_id=depot.id,
                    charger_type=charger_type,
                    start_time=start_time,
                    end_time=end_time,
                    energy_added=energy_needed
                )

        return None
    
    def _evaluate_solution_fast(self, solution: Solution):
        """快速解评估 - 修复目标函数计算逻辑"""
        total_passengers = 0
        hard_penalty = 0.0  # Only for feasibility constraints

        # Count number of buses used (buses with at least one trip)
        buses_used = 0

        for bus_tour in solution.bus_tours:
            # Count passengers evacuated (正确的计算方式：统计实际上车的乘客)
            for trip in bus_tour.trips:
                for passengers_at_stop in trip.passengers_boarded:
                    total_passengers += len(passengers_at_stop)

            # Count buses that have at least one trip
            if len(bus_tour.trips) > 0:
                buses_used += 1

            # Check feasibility constraints
            bus = self.buses[bus_tour.bus_id]

            # Time constraint penalty (hard constraint)
            if bus_tour.final_return_time > self.planning_horizon + self.return_buffer:
                hard_penalty += 1000 * (bus_tour.final_return_time - self.planning_horizon - self.return_buffer)

            # SoC constraint penalty (hard constraint)
            current_soc = bus.max_soc
            for trip in bus_tour.trips:
                current_soc -= trip.energy_consumed
                if current_soc < bus.min_soc:
                    hard_penalty += 1000 * (bus.min_soc - current_soc)

                # Add charging energy
                for charging in bus_tour.charging_events:
                    if charging.start_time >= trip.departure_times[-1]:
                        current_soc += charging.energy_added
                        break

        # Vehicle usage penalty: encourage using fewer buses (soft constraint)
        vehicle_usage_penalty = buses_used * self.vehicle_usage_penalty_weight

        # Total penalty = hard constraints + soft constraints
        total_penalty = hard_penalty + vehicle_usage_penalty

        solution.total_passengers_evacuated = total_passengers
        solution.objective_value = total_passengers
        solution.penalty_value = total_penalty
        solution.is_feasible = (hard_penalty == 0)  # Only based on hard constraints
    
    def parallel_destroy_repair_cycle(self, current_solutions: List[Solution],
                                    temperature: float) -> List[Solution]:
        """并行破坏-修复循环"""
        # Note: temperature parameter is kept for interface compatibility but not used in this simplified version

        # 准备批量数据
        num_solutions = len(current_solutions)
        num_remove_list = [max(1, random.randint(1, 5)) for _ in range(num_solutions)]

        # 随机选择破坏算子
        destroy_ops = np.random.choice(
            self.operator_evaluator.destroy_operators,
            size=num_solutions,
            p=self.operator_evaluator.destroy_weights / self.operator_evaluator.destroy_weights.sum()
        )

        # 并行破坏阶段
        destroyed_solutions = [None] * num_solutions  # 预先初始化列表

        # 按算子类型分组处理
        random_mask = (destroy_ops == 'random_trip_removal')
        route_mask = (destroy_ops == 'route_based_removal')
        time_mask = (destroy_ops == 'time_based_removal')

        if np.any(random_mask):
            random_solutions = [current_solutions[i] for i in range(num_solutions) if random_mask[i]]
            random_removes = [num_remove_list[i] for i in range(num_solutions) if random_mask[i]]
            random_results = self.destroy_operators.parallel_random_removal(random_solutions, random_removes)

            # 插入结果
            random_idx = 0
            for i in range(num_solutions):
                if random_mask[i]:
                    destroyed_solutions[i] = random_results[random_idx]
                    random_idx += 1

        if np.any(route_mask):
            route_solutions = [current_solutions[i] for i in range(num_solutions) if route_mask[i]]
            route_removes = [num_remove_list[i] for i in range(num_solutions) if route_mask[i]]
            route_results = self.destroy_operators.parallel_route_based_removal(
                route_solutions, route_removes, self.routes)

            # 插入结果
            route_idx = 0
            for i in range(num_solutions):
                if route_mask[i]:
                    destroyed_solutions[i] = route_results[route_idx]
                    route_idx += 1

        # 处理时间基础移除（简化版）- 使用time_mask变量
        if np.any(time_mask):
            for i in range(num_solutions):
                if time_mask[i] and destroyed_solutions[i] is None:
                    destroyed_solutions[i] = current_solutions[i].copy()

        # 处理剩余未处理的解
        for i in range(num_solutions):
            if destroyed_solutions[i] is None:
                destroyed_solutions[i] = current_solutions[i].copy()

        # 并行修复阶段
        repaired_solutions = self.repair_operators.vectorized_greedy_insertion(destroyed_solutions)

        # 评估所有解
        for solution in repaired_solutions:
            self._evaluate_solution_fast(solution)

        return repaired_solutions
    
    def solve_parallel(self) -> Solution:
        """并行ALNS求解"""
        print("🚀 启动高性能并行ALNS算法...")
        
        # 生成多个初始解
        print("生成初始解群...")
        current_solutions = []
        for i in range(self.parallel_solutions):
            solution = self.generate_initial_solution_fast()
            current_solutions.append(solution)
        
        # 选择最佳初始解
        best_solution = max(current_solutions, 
                           key=lambda s: s.objective_value - s.penalty_value)
        
        print(f"初始最佳解: {best_solution.objective_value} 乘客, "
              f"罚分: {best_solution.penalty_value}")
        
        # 主循环
        temperature = self.temperature_start
        iteration = 0
        
        start_time = time.time()
        
        while iteration < self.max_iterations and temperature > self.temperature_end:
            iteration += 1
            
            # 并行破坏-修复
            new_solutions = self.parallel_destroy_repair_cycle(current_solutions, temperature)
            
            # 接受判断（向量化）
            current_scores = np.array([s.objective_value - s.penalty_value for s in current_solutions])
            new_scores = np.array([s.objective_value - s.penalty_value for s in new_solutions])
            
            # 向量化接受概率计算
            score_diffs = new_scores - current_scores
            accept_probs = np.where(score_diffs >= 0, 1.0, 
                                  np.exp(score_diffs / temperature))
            
            # 随机接受判断
            random_vals = np.random.random(len(accept_probs))
            accept_mask = random_vals < accept_probs
            
            # 更新解
            for i in range(len(current_solutions)):
                if accept_mask[i]:
                    current_solutions[i] = new_solutions[i]
                    
                    # 更新全局最佳解
                    if new_scores[i] > (best_solution.objective_value - best_solution.penalty_value):
                        best_solution = new_solutions[i].copy()
                        print(f"迭代 {iteration}: 发现新最佳解! "
                              f"乘客: {best_solution.objective_value}, "
                              f"罚分: {best_solution.penalty_value}")
            
            # 更新算子权重
            if iteration % 100 == 0:
                self.operator_evaluator.update_weights_vectorized()
            
            # 温度冷却
            temperature *= self.alpha
            
            # 进度报告
            if iteration % 200 == 0:
                elapsed_time = time.time() - start_time
                current_best_score = best_solution.objective_value - best_solution.penalty_value
                avg_score = np.mean([s.objective_value - s.penalty_value for s in current_solutions])
                
                print(f"迭代 {iteration}: 最佳={current_best_score:.1f}, "
                      f"平均={avg_score:.1f}, 温度={temperature:.2f}, "
                      f"用时={elapsed_time:.1f}s")
        
        total_time = time.time() - start_time
        print(f"\n🎉 高性能ALNS完成!")
        print(f"总迭代数: {iteration}")
        print(f"总用时: {total_time:.2f}秒")
        print(f"平均每迭代: {total_time/iteration:.4f}秒")
        print(f"最佳解: {best_solution.objective_value} 乘客")
        print(f"罚分: {best_solution.penalty_value}")
        print(f"可行性: {best_solution.is_feasible}")
        
        return best_solution

    def solve(self) -> Solution:
        """Main solve method for compatibility with original interface"""
        return self.solve_parallel()

def create_example_problem(depot_info_file, route_info_file, passenger_data_file="passenger_demand_data.xlsx"):
    """Create a small example problem for testing"""

    # Load depot data from Excel file
    depot_data_list, vehicle_counts = load_depot_data_from_excel(depot_info_file)

    # Create depots from Excel data or fallback to default
    if depot_data_list is not None:
        depots = []
        for depot_data in depot_data_list:
            depot = Depot(
                id=depot_data['id'],
                location=depot_data['location'],
                chargers=depot_data['chargers'],
                charging_power=depot_data['charging_power']
            )
            depots.append(depot)

    # Load route data from Excel file
    routes_from_excel, _ = load_route_data_from_excel(route_info_file)

    # Use Excel route data if available, otherwise fallback to default generation
    if routes_from_excel is not None:
        routes = routes_from_excel
        # print(f"✅ 使用从Excel文件加载的 {len(routes)} 条路线")

    # Create buses using vehicle counts from Excel or default values
    bus_num = vehicle_counts  # 从Excel文件读取的每个车场的公交车数量
    buses = []
    for depot_id, num_buses in enumerate(bus_num):
        for _ in range(num_buses):
            buses.append(Bus(id=len(buses), home_depot=depot_id, capacity=79, max_soc=450.0, min_soc=90.0))
    print(f"🚌 创建公交车: {[f'Depot {len(buses)}辆']}")

    # Load passenger data from Excel file
    passenger_demand_df = load_passenger_data_from_excel(passenger_data_file)

    # Create passengers from Excel data if available, otherwise use random generation
    if passenger_demand_df is not None:
        passengers = create_passengers_from_excel_data(passenger_demand_df)
    else:
        passengers = create_passengers_random_fallback(routes)

    return buses, routes, depots, passengers

if __name__ == "__main__":
    import time
    start_time = time.time()

    print("🚀 高性能电动公交疏散调度系统")
    print("=" * 60)

    # 创建问题实例
    buses, routes, depots, passengers = create_example_problem(
        depot_info_file="depot_information_example.xlsx",
        route_info_file="route_information_example.xlsx",
        passenger_data_file="passenger_demand_example.xlsx"
    )
    
    # 初始化高性能ALNS求解器
    alns = HighPerformanceElectricBusEvacuationALNS(
        buses=buses,
        routes=routes,
        depots=depots,
        passengers=passengers,
        planning_horizon=180.0,  # 3 hours
        return_buffer=60.0      # 60 minutes buffer
    )
    
    # 求解
    best_solution = alns.solve()

    # Print detailed solution (matching original format)
    print("\n" + "="*50)
    print("DETAILED SOLUTION")
    print("="*50)

    for i, bus_tour in enumerate(best_solution.bus_tours):
        # Only print bus tours that have executed trips
        if len(bus_tour.trips) == 0:
            continue

        # Calculate battery state for this bus
        bus = buses[i]
        initial_soc = bus.current_soc

        # Calculate final SoC after all trips and charging
        current_soc = initial_soc
        for trip in bus_tour.trips:
            current_soc -= trip.energy_consumed
        for charging_event in bus_tour.charging_events:
            current_soc += charging_event.energy_added

        final_soc = current_soc
        soc_change = final_soc - initial_soc

        print(f"\nBus {i} Schedule:")
        print(f"  🔋 Initial SoC: {initial_soc:.2f} kWh ({initial_soc/bus.max_soc*100:.1f}%)")
        print(f"  🔋 Final SoC: {final_soc:.2f} kWh ({final_soc/bus.max_soc*100:.1f}%)")
        print(f"  🔋 SoC Change: {soc_change:+.2f} kWh")
        print(f"  🚌 Number of trips: {len(bus_tour.trips)}")
        print(f"  ⚡ Number of charging events: {len(bus_tour.charging_events)}")
        print(f"  ⏰ Final return time: {bus_tour.final_return_time:.2f} min")

        # Show charging events if any
        if bus_tour.charging_events:
            total_charged = sum(event.energy_added for event in bus_tour.charging_events)
            print(f"  ⚡ Total energy charged: {total_charged:.2f} kWh")

        # Show trip details
        trip_soc = initial_soc
        for j, trip in enumerate(bus_tour.trips):
            passengers_picked = sum(len(p) for p in trip.passengers_boarded)
            trip_soc -= trip.energy_consumed
            print(f"    Trip {j+1}: Route {trip.route_id}, "
                  f"Departure: {trip.departure_time:.2f} min, "
                  f"Passengers: {passengers_picked}, "
                  f"Energy: {trip.energy_consumed:.2f} kWh, "
                  f"SoC after trip: {trip_soc:.2f} kWh")

    end_time = time.time()
    print(f"\nTotal execution time: {end_time - start_time:.2f} seconds")