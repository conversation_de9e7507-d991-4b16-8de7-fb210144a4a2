#!/usr/bin/env python3
"""
测试修复后的初始解生成函数
验证硬约束满足情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from electric_bus_evacuation_alns import *

def test_constraint_satisfaction(solution, alns_instance, method_name):
    """测试解是否满足所有硬约束"""
    print(f"\n🔍 测试 {method_name} 生成的解:")
    print(f"   总乘客数: {solution.total_passengers_evacuated}")
    print(f"   目标值: {solution.objective_value}")
    print(f"   惩罚值: {solution.penalty_value}")
    print(f"   可行性: {'✅ 可行' if solution.is_feasible else '❌ 不可行'}")
    
    # 详细约束检查
    constraint_violations = {
        'time_violations': 0,
        'soc_violations': 0,
        'capacity_violations': 0
    }
    
    buses_with_trips = 0
    total_trips = 0
    
    for bus_tour in solution.bus_tours:
        if len(bus_tour.trips) > 0:
            buses_with_trips += 1
            total_trips += len(bus_tour.trips)
            bus = alns_instance.buses[bus_tour.bus_id]
            
            # 检查时间约束
            if bus_tour.final_return_time > alns_instance.planning_horizon + alns_instance.return_buffer:
                constraint_violations['time_violations'] += 1
                print(f"   ⚠️  Bus {bus_tour.bus_id}: 时间违反 - 返回时间 {bus_tour.final_return_time:.1f} > {alns_instance.planning_horizon + alns_instance.return_buffer}")
            
            # 检查电量约束
            current_soc = bus.max_soc
            for trip in bus_tour.trips:
                current_soc -= trip.energy_consumed
                if current_soc < bus.min_soc:
                    constraint_violations['soc_violations'] += 1
                    print(f"   ⚠️  Bus {bus_tour.bus_id}: 电量违反 - SoC {current_soc:.1f} < {bus.min_soc}")
                
                # 检查容量约束
                max_load = max(trip.passenger_load) if trip.passenger_load else 0
                if max_load > bus.capacity:
                    constraint_violations['capacity_violations'] += 1
                    print(f"   ⚠️  Bus {bus_tour.bus_id}: 容量违反 - 载客 {max_load} > {bus.capacity}")
                
                # 添加充电能量
                for charging in bus_tour.charging_events:
                    if charging.start_time >= trip.departure_times[-1]:
                        current_soc += charging.energy_added
                        break
    
    print(f"   使用车辆数: {buses_with_trips}/{len(alns_instance.buses)}")
    print(f"   总行程数: {total_trips}")
    print(f"   约束违反统计:")
    print(f"     时间约束违反: {constraint_violations['time_violations']}")
    print(f"     电量约束违反: {constraint_violations['soc_violations']}")
    print(f"     容量约束违反: {constraint_violations['capacity_violations']}")
    
    # 总体评估
    total_violations = sum(constraint_violations.values())
    if total_violations == 0:
        print(f"   🎉 {method_name}: 所有硬约束都满足!")
    else:
        print(f"   ❌ {method_name}: 发现 {total_violations} 个约束违反")
    
    return total_violations == 0, constraint_violations

def main():
    """主测试函数"""
    
    print("🚌 初始解生成函数约束满足测试")
    print("="*60)
    
    # 创建测试问题
    try:
        buses, routes, depots, passengers = create_example_problem(
            depot_info_file="depot_information_example.xlsx",
            route_info_file="route_information_example.xlsx",
            passenger_data_file="passenger_demand_example.xlsx"
        )
        
        print(f"📊 测试问题规模:")
        print(f"   车辆数: {len(buses)}")
        print(f"   路线数: {len(routes)}")
        print(f"   车场数: {len(depots)}")
        print(f"   乘客数: {len(passengers)}")
        
        # 初始化ALNS实例
        alns = ElectricBusEvacuationALNS(
            buses=buses,
            routes=routes,
            depots=depots,
            passengers=passengers,
            planning_horizon=180.0,
            return_buffer=60.0
        )
        
        # 测试三种初始解生成方法
        methods = [
            ("原版方法", alns.generate_initial_solution),
            ("改进版方法", alns.generate_initial_solution_improved),
            ("需求感知方法", alns.generate_initial_solution_demand_aware)
        ]
        
        results = {}
        
        for method_name, method_func in methods:
            try:
                print(f"\n{'='*20} {method_name} {'='*20}")
                solution = method_func()
                is_feasible, violations = test_constraint_satisfaction(solution, alns, method_name)
                results[method_name] = {
                    'feasible': is_feasible,
                    'violations': violations,
                    'passengers': solution.total_passengers_evacuated,
                    'penalty': solution.penalty_value
                }
            except Exception as e:
                print(f"❌ {method_name} 执行失败: {str(e)}")
                results[method_name] = {'error': str(e)}
        
        # 结果汇总
        print(f"\n{'='*20} 测试结果汇总 {'='*20}")
        
        for method_name, result in results.items():
            if 'error' in result:
                print(f"{method_name}: ❌ 执行失败")
            else:
                status = "✅ 约束满足" if result['feasible'] else "❌ 约束违反"
                print(f"{method_name}: {status} | 乘客: {result['passengers']} | 惩罚: {result['penalty']:.1f}")
        
        # 性能比较
        print(f"\n📈 性能比较:")
        feasible_results = {k: v for k, v in results.items() if 'error' not in v and v['feasible']}
        
        if feasible_results:
            best_method = max(feasible_results.items(), key=lambda x: x[1]['passengers'])
            print(f"   最佳方法: {best_method[0]} (疏散 {best_method[1]['passengers']} 名乘客)")
            
            # 车辆利用率分析
            print(f"\n🚌 车辆利用率分析:")
            for method_name, method_func in methods:
                if method_name in feasible_results:
                    solution = method_func()
                    buses_used = sum(1 for tour in solution.bus_tours if len(tour.trips) > 0)
                    utilization = buses_used / len(buses) * 100
                    print(f"   {method_name}: {buses_used}/{len(buses)} 辆车 ({utilization:.1f}%)")
        else:
            print("   ⚠️  没有生成可行解的方法!")
    
    except Exception as e:
        print(f"❌ 测试初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()